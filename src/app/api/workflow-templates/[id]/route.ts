import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * DELETE /api/workflow-templates/[id]
 * Deletes a workflow template record
 * Handles 409 ConflictException responses when deletion is not possible
 * @param {NextRequest} request - The incoming request
 * @param {Object} params - Route parameters
 * @param {string} params.id - The workflow template ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Workflow template ID is required" },
        { status: 400 }
      );
    }

    // Delete workflow template in backend
    const backendUrl = `${apiUrl}/workflow-templates/${id}`;

    const response = await fetch(backendUrl, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      // Forward the exact error response from backend, including 409 conflicts
      return NextResponse.json(data, { status: response.status });
    }
  } catch (error) {
    console.error("Error deleting workflow template:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
