"use client";
import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog } from "@/components/ui/dialog";
import { Check, Edit, Trash, Eye } from "lucide-react";
import { useRemoveImmigration } from "@/hooks/use-query";
import { Badge } from "../ui/badge";
import { VisibilityToggle } from "./visibility-toggle";
import ImmigrationForm from "./immigration-form";

/**
 * Immigration services display component with modal-based editing
 * Displays immigration services in a grid layout with edit and delete functionality
 * Uses modal dialogs for editing to avoid navigation and unnecessary API calls
 * @param {Object} props - Component props
 * @param {IImmigration[]} props.data - Array of immigration services to display
 * @return {JSX.Element} The immigration services grid component
 */
const Immigrations = ({ data }: { data: IImmigration[] }) => {
  const { mutate: remove, isPending } = useRemoveImmigration();
  const [editingImmigration, setEditingImmigration] =
    useState<IImmigration | null>(null);

  /**
   * <PERSON><PERSON> opening the edit modal with selected immigration data
   * @param {IImmigration} immigration - The immigration service to edit
   */
  const handleEditClick = (immigration: IImmigration) => {
    setEditingImmigration(immigration);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {data.map((pkg) => (
        <Card className="w-full h-full flex flex-col relative" key={pkg.id}>
          <CardHeader className="space-y-1">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl font-bold">{pkg.name}</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-muted-foreground" />
                <VisibilityToggle
                  immigrationId={pkg.id!}
                  initialVisibility={pkg.website_visible ?? false}
                />
              </div>
            </div>
            <div className="flex items-baseline gap-1">
              <span className="text-3xl font-bold">€{pkg.amount}</span>
            </div>
          </CardHeader>
          <CardContent className="mt-4 flex-grow">
            <ul className="space-y-3">
              {pkg.service.map((service, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Check className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                  <span>{service}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter className="justify-end gap-x-4">
            <Button
              className="bg-[#ffebe6] text-[#fd381d] hover:bg-[#ffebe6] "
              disabled={isPending}
              onClick={() => remove(pkg.id as string)}
            >
              <Trash
                className="-ms-1 me-2 opacity-60"
                size={16}
                strokeWidth={2}
                aria-hidden="true"
              />
              Delete
            </Button>
            <Button onClick={() => handleEditClick(pkg)}>
              <Edit
                className="-ms-1 me-2 opacity-60"
                size={16}
                strokeWidth={2}
                aria-hidden="true"
              />
              Edit
            </Button>
          </CardFooter>
          {pkg?.order && (
            <Badge className=" bg-[#404BD0]/10 text-[#404BD0] hover:bg-[#404BD0]/10 hover:text-[#404BD0] absolute top-2 right-2">
              {pkg.order}
            </Badge>
          )}
        </Card>
      ))}

      {/* Edit Immigration Modal Dialog */}
      <Dialog
        open={!!editingImmigration}
        onOpenChange={(open) => !open && setEditingImmigration(null)}
      >
        {editingImmigration && (
          <ImmigrationForm
            immigration={editingImmigration}
            onSuccess={() => setEditingImmigration(null)}
          />
        )}
      </Dialog>
    </div>
  );
};

export default Immigrations;
