{"name": "nextjs-app-type", "version": "0.6.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "format": "prettier --write .", "test-all": "npm run check-format && npm run check-lint && npm run check-types && npm run build", "prepare": "husky install"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.45.1", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-blockquote": "^2.10.3", "@tiptap/extension-bold": "^2.10.3", "@tiptap/extension-color": "^2.10.3", "@tiptap/extension-document": "^2.10.2", "@tiptap/extension-heading": "^2.10.3", "@tiptap/extension-image": "^2.10.3", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-paragraph": "^2.10.2", "@tiptap/extension-text": "^2.10.2", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/extension-text-style": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@tiptap/extension-youtube": "^2.10.3", "@tiptap/react": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "axios": "^1.7.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^4.2.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "geist": "^1.3.0", "lucide-react": "^0.395.0", "next": "14.2.4", "next-auth": "^4.24.10", "next-themes": "^0.3.0", "react": "^18", "react-aria-components": "^1.7.1", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.52.0", "recharts": "^2.15.0", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-google": "^0.14.0", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.2", "husky": "^9.0.11", "postcss": "^8", "prettier": "^3.3.2", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.4", "typescript": "^5"}}